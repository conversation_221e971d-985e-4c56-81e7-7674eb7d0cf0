#!/usr/bin/env python3
"""
Test script for the weed tracking system
This script simulates detections to test the tracking logic without hardware
"""

import numpy as np
import cv2
import time
from ultralytics import YOLO

# Import the tracker from the main file
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Copy the WeedTracker class here for testing
class WeedTracker:
    def __init__(self, max_disappeared=10, max_distance=40):
        self.next_id = 0
        self.objects = {}  # id -> {center, last_seen, state, confidence}
        self.burned_ids = set()
        self.max_disappeared = max_disappeared  # frames before considering object lost
        self.max_distance = max_distance  # max pixel distance for association
        self.frame_count = 0
        self.prev_frame_detections = []  # For motion compensation
        self.global_motion = np.array([0.0, 0.0])  # Estimated camera motion
        
    def update(self, detections):
        """Update tracker with new detections"""
        self.frame_count += 1
        
        print(f"Tracker update: Frame {self.frame_count}, {len(detections)} detections, {len(self.objects)} existing objects")
        
        # If no detections, just age existing objects
        if len(detections) == 0:
            self._age_objects()
            self.prev_frame_detections = []
            return []
        
        # If no existing objects, create new ones
        if len(self.objects) == 0:
            print("  No existing objects, creating new ones")
            for detection in detections:
                new_id = self._create_new_object(detection)
                print(f"  Created object {new_id} at {detection}")
        else:
            # Match detections to existing objects
            print("  Matching detections to existing objects")
            self._match_detections(detections)
        
        # Clean up old objects
        self._cleanup_objects()
        
        # Store current detections for next frame
        self.prev_frame_detections = detections.copy()
        
        # Return active objects with their IDs
        active_objects = [(obj_id, obj_data['center']) for obj_id, obj_data in self.objects.items() 
                         if obj_data['state'] == 'active']
        print(f"  Returning {len(active_objects)} active objects")
        return active_objects
    
    def _create_new_object(self, center):
        """Create a new tracked object"""
        self.objects[self.next_id] = {
            'center': center,
            'last_seen': self.frame_count,
            'state': 'active',
            'confidence': 1.0,
            'birth_frame': self.frame_count
        }
        self.next_id += 1
        return self.next_id - 1
    
    def _match_detections(self, detections):
        """Match detections to existing objects using simple distance matching"""
        # Get active objects
        active_objects = {obj_id: obj_data for obj_id, obj_data in self.objects.items() 
                         if obj_data['state'] in ['active', 'lost']}
        
        if len(active_objects) == 0:
            # No active objects, create new ones
            for detection in detections:
                self._create_new_object(detection)
            return
        
        # Simple nearest neighbor matching
        used_detections = set()
        used_objects = set()
        
        # For each detection, find the closest object
        for i, detection in enumerate(detections):
            best_obj_id = None
            min_distance = self.max_distance
            
            for obj_id, obj_data in active_objects.items():
                if obj_id in used_objects:
                    continue
                    
                distance = np.linalg.norm(np.array(detection) - np.array(obj_data['center']))
                if distance < min_distance:
                    min_distance = distance
                    best_obj_id = obj_id
            
            if best_obj_id is not None:
                # Update the matched object
                self.objects[best_obj_id]['center'] = detection
                self.objects[best_obj_id]['last_seen'] = self.frame_count
                self.objects[best_obj_id]['state'] = 'active'
                self.objects[best_obj_id]['confidence'] = min(1.0, self.objects[best_obj_id]['confidence'] + 0.2)
                used_objects.add(best_obj_id)
                used_detections.add(i)
                print(f"  Matched detection {i} to object {best_obj_id} (dist: {min_distance:.1f})")
            else:
                # Create new object for unmatched detection
                new_id = self._create_new_object(detection)
                print(f"  Created new object {new_id} for detection {i}")
        
        # Mark unmatched objects as lost
        for obj_id in active_objects:
            if obj_id not in used_objects:
                if self.objects[obj_id]['state'] == 'active':
                    self.objects[obj_id]['state'] = 'lost'
                    self.objects[obj_id]['confidence'] = max(0.0, self.objects[obj_id]['confidence'] - 0.3)
                    print(f"  Object {obj_id} marked as lost")
    
    def _age_objects(self):
        """Age all objects when no detections"""
        for obj_id in self.objects:
            if self.objects[obj_id]['state'] == 'active':
                self.objects[obj_id]['state'] = 'lost'
            self.objects[obj_id]['confidence'] = max(0.0, self.objects[obj_id]['confidence'] - 0.1)
    
    def _cleanup_objects(self):
        """Remove objects that have been lost for too long"""
        to_remove = []
        for obj_id, obj_data in self.objects.items():
            frames_since_seen = self.frame_count - obj_data['last_seen']
            if (frames_since_seen > self.max_disappeared and 
                obj_data['state'] == 'lost' and 
                obj_id not in self.burned_ids):
                to_remove.append(obj_id)
        
        for obj_id in to_remove:
            print(f"  Removing old object {obj_id}")
            del self.objects[obj_id]
    
    def mark_burned(self, obj_id):
        """Mark an object as burned"""
        if obj_id in self.objects:
            self.burned_ids.add(obj_id)
            self.objects[obj_id]['state'] = 'burned'
    
    def is_burned(self, obj_id):
        """Check if object has been burned"""
        return obj_id in self.burned_ids
    
    def get_stats(self):
        """Get tracking statistics"""
        active_count = sum(1 for obj in self.objects.values() if obj['state'] == 'active')
        lost_count = sum(1 for obj in self.objects.values() if obj['state'] == 'lost')
        burned_count = len(self.burned_ids)
        return {
            'active': active_count,
            'lost': lost_count,
            'burned': burned_count,
            'total': len(self.objects)
        }

def test_tracking():
    """Test the tracking system with simulated detections"""
    tracker = WeedTracker(max_disappeared=5, max_distance=50)
    
    # Simulate some test scenarios
    test_scenarios = [
        # Frame 1: Two weeds appear
        [[100, 100], [300, 200]],
        # Frame 2: Same weeds, slightly moved
        [[105, 102], [295, 205]],
        # Frame 3: One weed disappears, one new appears
        [[110, 105], [400, 300]],
        # Frame 4: All weeds move
        [[115, 108], [405, 305]],
        # Frame 5: No detections
        [],
        # Frame 6: Original weed reappears
        [[120, 110]],
        # Frame 7: Camera moves (all positions shift)
        [[140, 130], [420, 325]],
    ]
    
    print("=== TRACKING TEST ===")
    
    for frame_num, detections in enumerate(test_scenarios, 1):
        print(f"\n--- Frame {frame_num} ---")
        print(f"Input detections: {detections}")
        
        tracked_objects = tracker.update(detections)
        
        print(f"Tracked objects: {[(obj_id, [int(c) for c in center]) for obj_id, center in tracked_objects]}")
        
        stats = tracker.get_stats()
        print(f"Stats: Active={stats['active']}, Lost={stats['lost']}, Burned={stats['burned']}, Total={stats['total']}")
        
        # Simulate burning the first object in frame 3
        if frame_num == 3 and len(tracked_objects) > 0:
            obj_id = tracked_objects[0][0]
            tracker.mark_burned(obj_id)
            print(f"Burned object {obj_id}")
        
        time.sleep(0.5)  # Pause for readability

if __name__ == "__main__":
    test_tracking()
