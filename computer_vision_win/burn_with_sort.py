from ultralytics import YOLO
import cv2
import freenect
import numpy as np
import time
import pyfirmata
import json
import glob
import os
from scipy.interpolate import griddata

# Load YOLO model
model = YOLO("best.pt")

# Arduino setup
board = pyfirmata.Arduino('/dev/ttyACM0')  # Change path as needed
it = pyfirmata.util.Iterator(board)
it.start()

# Define servo and laser pins
servo_x = board.get_pin('d:10:s')     # D10: Servo X
servo_y = board.get_pin('d:9:s')      # D9: Servo Y
laser = board.get_pin('d:8:o')        # D8: Laser ON/OFF

# Camera dimensions
CAMERA_WIDTH = 640
CAMERA_HEIGHT = 480

# Servo ranges (will be loaded from calibration)
SERVO_X_MIN = 70
SERVO_X_MAX = 120
SERVO_Y_MIN = 70
SERVO_Y_MAX = 122

# Simple tracking using SORT
burned_ids = set()  # Set of burned object IDs

# Calibration data
calibration_data = None
interpolator_x = None
interpolator_y = None

def get_kinect_frame():
    """Capture frame from Kinect v1"""
    _, _ = freenect.sync_get_depth()
    video, _ = freenect.sync_get_video()
    return cv2.cvtColor(video, cv2.COLOR_RGB2BGR)

def load_calibration():
    """Load the most recent calibration file"""
    global calibration_data, interpolator_x, interpolator_y
    global SERVO_X_MIN, SERVO_X_MAX, SERVO_Y_MIN, SERVO_Y_MAX

    files = glob.glob("servo_calibration_*.json")
    if not files:
        print("❌ No calibration files found! Creating fallback...")
        basic_calibration = {
            "timestamp": "fallback",
            "servo_ranges": {"x_min": SERVO_X_MIN, "x_max": SERVO_X_MAX, "y_min": SERVO_Y_MIN, "y_max": SERVO_Y_MAX},
            "camera_dimensions": {"width": CAMERA_WIDTH, "height": CAMERA_HEIGHT},
            "calibration_points": [
                {"camera": [136, 234], "servo": [76, 92]},
                {"camera": [320, 240], "servo": [88, 85]},
                {"camera": [500, 150], "servo": [100, 78]}
            ]
        }
        with open("servo_calibration_fallback.json", 'w') as f:
            json.dump(basic_calibration, f, indent=2)
        files = ["servo_calibration_fallback.json"]

    latest_file = max(files, key=os.path.getctime)
    try:
        with open(latest_file, 'r') as f:
            calibration_data = json.load(f)

        print(f"📂 Loaded calibration from: {latest_file}")

        # Update servo ranges
        ranges = calibration_data['servo_ranges']
        SERVO_X_MIN = ranges['x_min']
        SERVO_X_MAX = ranges['x_max']
        SERVO_Y_MIN = ranges['y_min']
        SERVO_Y_MAX = ranges['y_max']

        # Create interpolators
        points = calibration_data['calibration_points']
        camera_coords = np.array([point['camera'] for point in points])
        servo_x_coords = np.array([point['servo'][0] for point in points])
        servo_y_coords = np.array([point['servo'][1] for point in points])

        def safe_interpolator_x(x, y):
            try:
                if len(points) >= 3:
                    result = griddata(camera_coords, servo_x_coords, (x, y), method='linear', fill_value=np.nan)
                    if np.isnan(result):
                        result = griddata(camera_coords, servo_x_coords, (x, y), method='nearest')
                    return result
                else:
                    return griddata(camera_coords, servo_x_coords, (x, y), method='nearest')
            except:
                return griddata(camera_coords, servo_x_coords, (x, y), method='nearest')

        def safe_interpolator_y(x, y):
            try:
                if len(points) >= 3:
                    result = griddata(camera_coords, servo_y_coords, (x, y), method='linear', fill_value=np.nan)
                    if np.isnan(result):
                        result = griddata(camera_coords, servo_y_coords, (x, y), method='nearest')
                    return result
                else:
                    return griddata(camera_coords, servo_y_coords, (x, y), method='nearest')
            except:
                return griddata(camera_coords, servo_y_coords, (x, y), method='nearest')

        interpolator_x = safe_interpolator_x
        interpolator_y = safe_interpolator_y
        return True

    except Exception as e:
        print(f"❌ Error loading calibration: {e}")
        return False

def camera_to_servo_angles(camera_x, camera_y):
    """Convert camera coordinates to servo angles"""
    if interpolator_x is None or interpolator_y is None:
        return 88, 85

    try:
        servo_x = interpolator_x(camera_x, camera_y)
        servo_y = interpolator_y(camera_x, camera_y)

        if servo_x is None or servo_y is None or np.isnan(servo_x) or np.isnan(servo_y):
            return 88, 85

        servo_x = max(SERVO_X_MIN, min(SERVO_X_MAX, float(servo_x)))
        servo_y = max(SERVO_Y_MIN, min(SERVO_Y_MAX, float(servo_y)))
        return int(servo_x), int(servo_y)
    except:
        return 88, 85

def compute_center(box):
    """Compute center of bounding box"""
    x1, y1, x2, y2 = box
    return [(x1 + x2) / 2, (y1 + y2) / 2]

def main():
    """Main weed burning loop with SORT tracking"""
    print("🔥 WEED BURNING SYSTEM - SORT TRACKING")
    print("=" * 50)

    if not load_calibration():
        print("❌ Cannot start without calibration data!")
        return

    # Initialize servo to center
    servo_x.write(88)
    servo_y.write(85)
    time.sleep(1)

    print("\n🎯 Starting weed detection and burning with SORT...")
    print("Press 'q' to quit")

    frame_count = 0
    last_new_detection_frame = 0  # Track when we last detected a new weed

    try:
        while True:
            frame = get_kinect_frame()
            frame = cv2.resize(frame, (CAMERA_WIDTH, CAMERA_HEIGHT))
            frame_count += 1

            print(f"\n--- Frame {frame_count} ---")

            # Reset tracking every 100 frames if no new weeds detected
            # This helps ensure new weeds get fresh IDs
            if frame_count - last_new_detection_frame > 100:
                print("🔄 Resetting tracker - no new weeds detected recently")
                # Force a fresh tracking session
                try:
                    results = model.track(frame, conf=0.3, persist=False, tracker="sort.yaml")
                    last_new_detection_frame = frame_count
                except:
                    pass

            # Try SORT trackers in order of preference
            try:
                # First try classic SORT
                results = model.track(frame, conf=0.3, persist=True, tracker="sort.yaml")
                tracker_name = "SORT"
            except:
                try:
                    # Fallback to BoTSORT (SORT-based)
                    results = model.track(frame, conf=0.3, persist=True, tracker="botsort.yaml")
                    tracker_name = "BoTSORT"
                except:
                    try:
                        # Fallback to ByteTrack
                        results = model.track(frame, conf=0.3, persist=True, tracker="bytetrack.yaml")
                        tracker_name = "ByteTrack"
                    except:
                        # Last resort - no tracking
                        results = model(frame, conf=0.3, stream=True)
                        tracker_name = "No Tracking"

            print(f"Using tracker: {tracker_name}")

            # Count total detections and separate new vs burned
            total_detections = 0
            new_detections = 0
            burned_detections = 0

            # First pass: count detections
            for result in results:
                if result.boxes is not None:
                    total_detections += len(result.boxes)
                    for box in result.boxes:
                        # Get track ID
                        if hasattr(box, 'id') and box.id is not None:
                            track_id = int(box.id[0])
                        else:
                            x1, y1, x2, y2 = map(int, box.xyxy[0])
                            track_id = f"pos_{int(x1/50)}_{int(y1/50)}"

                        if track_id in burned_ids:
                            burned_detections += 1
                        else:
                            new_detections += 1

            print(f"Total detections: {total_detections} (New: {new_detections}, Burned: {burned_detections})")
            print(f"Currently burned IDs: {burned_ids}")

            # Process ALL detections - don't skip any
            weeds_burned_this_frame = 0

            for result in results:
                boxes = result.boxes
                if boxes is None:
                    continue

                for box in boxes:
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    confidence = float(box.conf[0])
                    
                    # Get track ID if available
                    if hasattr(box, 'id') and box.id is not None:
                        track_id = int(box.id[0])
                        has_id = True
                    else:
                        # If no tracking, use position-based ID
                        track_id = f"pos_{int(x1/50)}_{int(y1/50)}"  # Grid-based ID
                        has_id = False
                    
                    center = compute_center((x1, y1, x2, y2))
                    box_center_x, box_center_y = int(center[0]), int(center[1])
                    servo_angle_x, servo_angle_y = camera_to_servo_angles(box_center_x, box_center_y)

                    print(f"Detection: ID={track_id}, Center=({box_center_x}, {box_center_y}), Conf={confidence:.2f}, HasID={has_id}")

                    # Check if already burned
                    if track_id in burned_ids:
                        color = (0, 0, 255)  # Red for burned
                        label = f"ID:{track_id} (BURNED)"
                        print(f"   Skipping - already burned")
                    else:
                        # Burn if confidence is good
                        if confidence > 0.25:  # Lower threshold
                            print(f"🔥 BURNING ID:{track_id} - Confidence: {confidence:.2f}")

                            # Move servo and burn
                            servo_x.write(servo_angle_x)
                            servo_y.write(servo_angle_y)
                            time.sleep(0.3)  # Quick servo movement

                            laser.write(1)
                            print(f"🎯 Laser ON at ({servo_angle_x}°, {servo_angle_y}°)")
                            time.sleep(1.5)  # Shorter burn time
                            laser.write(0)
                            print(f"🔥 Laser OFF - Burned ID:{track_id}")

                            burned_ids.add(track_id)
                            weeds_burned_this_frame += 1
                            last_new_detection_frame = frame_count  # Update last new detection

                            color = (0, 255, 0)  # Green for just burned
                            label = f"ID:{track_id} (JUST BURNED)"

                            # IMPORTANT: Continue processing other weeds in the same frame
                            # Don't break or stop here - let it detect and burn all new weeds
                        else:
                            color = (255, 255, 0)  # Yellow for low confidence
                            label = f"ID:{track_id} (LOW:{confidence:.2f})"
                            print(f"   Low confidence: {confidence:.2f} < 0.25")

                    # Draw detection
                    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                    cv2.putText(frame, label, (x1, y1 - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                    cv2.circle(frame, (box_center_x, box_center_y), 5, color, -1)
                    
                    # Show tracking info
                    if has_id:
                        cv2.putText(frame, f"Tracked", (x1, y2 + 15),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
                    else:
                        cv2.putText(frame, f"No Track", (x1, y2 + 15),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)

            # Print frame summary
            print(f"Frame {frame_count} Summary: Burned {weeds_burned_this_frame} new weeds this frame")
            print(f"Total burned so far: {len(burned_ids)}")

            # Draw stats
            cv2.putText(frame, f"Frame: {frame_count} | Tracker: {tracker_name} | Burned: {len(burned_ids)}",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(frame, f"New: {new_detections} | Burned: {burned_detections} | This frame: {weeds_burned_this_frame}",
                       (10, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

            cv2.imshow("SORT Tracking Weed Burner", frame)

            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

    except KeyboardInterrupt:
        print("\n🛑 Stopping...")

    finally:
        laser.write(0)
        servo_x.write(88)
        servo_y.write(85)
        board.exit()
        cv2.destroyAllWindows()
        print("✅ System shutdown complete")

if __name__ == "__main__":
    main()
