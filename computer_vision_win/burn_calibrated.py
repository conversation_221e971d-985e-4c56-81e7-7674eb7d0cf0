from ultralytics import YOLO
import cv2
import freenect
import numpy as np
import time
import pyfirmata
import json
import glob
import os
from scipy.interpolate import griddata

# Load YOLO model
model = YOLO("best.pt")

# Arduino setup
board = pyfirmata.Arduino('/dev/ttyACM0')  # Change path as needed
it = pyfirmata.util.Iterator(board)
it.start()

# Define servo and laser pins
servo_x = board.get_pin('d:10:s')     # D10: Servo X
servo_y = board.get_pin('d:9:s')      # D9: Servo Y
laser = board.get_pin('d:8:o')        # D8: Laser ON/OFF

# Camera dimensions
CAMERA_WIDTH = 640
CAMERA_HEIGHT = 480

# Servo ranges (will be loaded from calibration)
SERVO_X_MIN = 70
SERVO_X_MAX = 120
SERVO_Y_MIN = 70
SERVO_Y_MAX = 122

# Object tracking - Enhanced system
class WeedTracker:
    def __init__(self, max_disappeared=10, max_distance=80):
        self.next_id = 0
        self.objects = {}  # id -> {center, last_seen, state, confidence}
        self.burned_ids = set()
        self.max_disappeared = max_disappeared  # frames before considering object lost
        self.max_distance = max_distance  # max pixel distance for association
        self.frame_count = 0
        self.prev_frame_detections = []  # For motion compensation
        self.global_motion = np.array([0.0, 0.0])  # Estimated camera motion

    def update(self, detections):
        """Update tracker with new detections"""
        self.frame_count += 1

        # Estimate camera motion if we have previous detections
        if len(self.prev_frame_detections) > 0 and len(detections) > 0:
            self._estimate_camera_motion(detections)

        # Compensate for camera motion in existing objects
        if np.linalg.norm(self.global_motion) > 2:  # Only if significant motion
            self._compensate_motion()

        # If no detections, just age existing objects
        if len(detections) == 0:
            self._age_objects()
            self.prev_frame_detections = []
            return []

        # If no existing objects, create new ones
        if len(self.objects) == 0:
            for detection in detections:
                self._create_new_object(detection)
        else:
            # Match detections to existing objects
            self._match_detections(detections)

        # Clean up old objects
        self._cleanup_objects()

        # Store current detections for next frame
        self.prev_frame_detections = detections.copy()

        # Return active objects with their IDs
        return [(obj_id, obj_data['center']) for obj_id, obj_data in self.objects.items()
                if obj_data['state'] == 'active']

    def _create_new_object(self, center):
        """Create a new tracked object"""
        self.objects[self.next_id] = {
            'center': center,
            'last_seen': self.frame_count,
            'state': 'active',
            'confidence': 1.0,
            'birth_frame': self.frame_count
        }
        self.next_id += 1
        return self.next_id - 1

    def _match_detections(self, detections):
        """Match detections to existing objects using Hungarian algorithm approach"""
        import numpy as np

        # Get active objects
        active_objects = {obj_id: obj_data for obj_id, obj_data in self.objects.items()
                         if obj_data['state'] in ['active', 'lost']}

        if len(active_objects) == 0:
            # No active objects, create new ones
            for detection in detections:
                self._create_new_object(detection)
            return

        # Calculate distance matrix
        object_ids = list(active_objects.keys())
        distances = np.zeros((len(detections), len(object_ids)))

        for i, detection in enumerate(detections):
            for j, obj_id in enumerate(object_ids):
                obj_center = active_objects[obj_id]['center']
                distances[i, j] = np.linalg.norm(np.array(detection) - np.array(obj_center))

        # Simple greedy matching (can be improved with Hungarian algorithm)
        used_detections = set()
        used_objects = set()

        # Sort by distance and match greedily
        matches = []
        for _ in range(min(len(detections), len(object_ids))):
            min_dist = float('inf')
            best_detection = -1
            best_object = -1

            for i in range(len(detections)):
                if i in used_detections:
                    continue
                for j, obj_id in enumerate(object_ids):
                    if obj_id in used_objects:
                        continue
                    if distances[i, j] < min_dist and distances[i, j] < self.max_distance:
                        min_dist = distances[i, j]
                        best_detection = i
                        best_object = obj_id

            if best_detection != -1 and best_object != -1:
                matches.append((best_detection, best_object))
                used_detections.add(best_detection)
                used_objects.add(best_object)

        # Update matched objects
        for detection_idx, obj_id in matches:
            self.objects[obj_id]['center'] = detections[detection_idx]
            self.objects[obj_id]['last_seen'] = self.frame_count
            self.objects[obj_id]['state'] = 'active'
            self.objects[obj_id]['confidence'] = min(1.0, self.objects[obj_id]['confidence'] + 0.1)

        # Create new objects for unmatched detections
        for i, detection in enumerate(detections):
            if i not in used_detections:
                self._create_new_object(detection)

        # Mark unmatched objects as lost
        for obj_id in object_ids:
            if obj_id not in used_objects:
                if self.objects[obj_id]['state'] == 'active':
                    self.objects[obj_id]['state'] = 'lost'
                    self.objects[obj_id]['confidence'] = max(0.0, self.objects[obj_id]['confidence'] - 0.2)

    def _age_objects(self):
        """Age all objects when no detections"""
        for obj_id in self.objects:
            if self.objects[obj_id]['state'] == 'active':
                self.objects[obj_id]['state'] = 'lost'
            self.objects[obj_id]['confidence'] = max(0.0, self.objects[obj_id]['confidence'] - 0.1)

    def _cleanup_objects(self):
        """Remove objects that have been lost for too long"""
        to_remove = []
        for obj_id, obj_data in self.objects.items():
            frames_since_seen = self.frame_count - obj_data['last_seen']
            if (frames_since_seen > self.max_disappeared and
                obj_data['state'] == 'lost' and
                obj_id not in self.burned_ids):
                to_remove.append(obj_id)

        for obj_id in to_remove:
            del self.objects[obj_id]

    def mark_burned(self, obj_id):
        """Mark an object as burned"""
        if obj_id in self.objects:
            self.burned_ids.add(obj_id)
            self.objects[obj_id]['state'] = 'burned'

    def is_burned(self, obj_id):
        """Check if object has been burned"""
        return obj_id in self.burned_ids

    def _estimate_camera_motion(self, current_detections):
        """Estimate global camera motion between frames"""
        if len(self.prev_frame_detections) == 0 or len(current_detections) == 0:
            self.global_motion = np.array([0.0, 0.0])
            return

        # Simple motion estimation: find average displacement of closest points
        motions = []
        for curr_det in current_detections:
            min_dist = float('inf')
            closest_prev = None
            for prev_det in self.prev_frame_detections:
                dist = np.linalg.norm(np.array(curr_det) - np.array(prev_det))
                if dist < min_dist and dist < self.max_distance:
                    min_dist = dist
                    closest_prev = prev_det

            if closest_prev is not None:
                motion = np.array(curr_det) - np.array(closest_prev)
                motions.append(motion)

        if len(motions) > 0:
            # Use median to be robust against outliers
            motions = np.array(motions)
            self.global_motion = np.median(motions, axis=0)
        else:
            self.global_motion = np.array([0.0, 0.0])

    def _compensate_motion(self):
        """Compensate for estimated camera motion in tracked objects"""
        for obj_idbur    in self.objects:
            if self.objects[obj_id]['state'] in ['active', 'lost']:
                # Move object position by negative of camera motion
                old_center = np.array(self.objects[obj_id]['center'])
                new_center = old_center - self.global_motion
                self.objects[obj_id]['center'] = new_center.tolist()

    def get_stats(self):
        """Get tracking statistics"""
        active_count = sum(1 for obj in self.objects.values() if obj['state'] == 'active')
        lost_count = sum(1 for obj in self.objects.values() if obj['state'] == 'lost')
        burned_count = len(self.burned_ids)
        return {
            'active': active_count,
            'lost': lost_count,
            'burned': burned_count,
            'total': len(self.objects),
            'motion': np.linalg.norm(self.global_motion)
        }

# Initialize tracker
weed_tracker = WeedTracker(max_disappeared=15, max_distance=60)

# Calibration data
calibration_data = None
interpolator_x = None
interpolator_y = None

def get_kinect_frame():
    """Capture frame from Kinect v1"""
    _, _ = freenect.sync_get_depth()
    video, _ = freenect.sync_get_video()
    return cv2.cvtColor(video, cv2.COLOR_RGB2BGR)

def load_calibration():
    """Load the most recent calibration file"""
    global calibration_data, interpolator_x, interpolator_y
    global SERVO_X_MIN, SERVO_X_MAX, SERVO_Y_MIN, SERVO_Y_MAX

    files = glob.glob("servo_calibration_*.json")
    if not files:
        print("❌ No calibration files found! Please run calibrate_servo.py first.")
        print("💡 Creating a basic single-point calibration as fallback...")

        # Create a basic calibration with your original point
        basic_calibration = {
            "timestamp": "fallback",
            "servo_ranges": {
                "x_min": SERVO_X_MIN,
                "x_max": SERVO_X_MAX,
                "y_min": SERVO_Y_MIN,
                "y_max": SERVO_Y_MAX
            },
            "camera_dimensions": {
                "width": CAMERA_WIDTH,
                "height": CAMERA_HEIGHT
            },
            "calibration_points": [
                {"camera": [136, 234], "servo": [76, 92]},
                {"camera": [320, 240], "servo": [88, 85]},  # Center point
                {"camera": [500, 150], "servo": [100, 78]}  # Another point for interpolation
            ]
        }

        # Save basic calibration
        with open("servo_calibration_fallback.json", 'w') as f:
            json.dump(basic_calibration, f, indent=2)

        print("✅ Created fallback calibration file")
        files = ["servo_calibration_fallback.json"]

    latest_file = max(files, key=os.path.getctime)
    try:
        with open(latest_file, 'r') as f:
            calibration_data = json.load(f)

        print(f"📂 Loaded calibration from: {latest_file}")

        # Update servo ranges from calibration
        ranges = calibration_data['servo_ranges']
        SERVO_X_MIN = ranges['x_min']
        SERVO_X_MAX = ranges['x_max']
        SERVO_Y_MIN = ranges['y_min']
        SERVO_Y_MAX = ranges['y_max']

        # Create interpolators for smooth mapping
        points = calibration_data['calibration_points']
        if len(points) < 1:
            print("❌ Error: No calibration points found!")
            return False
        elif len(points) < 3:
            print("⚠️ Warning: Less than 3 calibration points. Using nearest neighbor interpolation.")

        # Extract camera and servo coordinates
        camera_coords = np.array([point['camera'] for point in points])
        servo_x_coords = np.array([point['servo'][0] for point in points])
        servo_y_coords = np.array([point['servo'][1] for point in points])

        # Create interpolators with error handling
        def safe_interpolator_x(x, y):
            try:
                if len(points) >= 3:
                    result = griddata(camera_coords, servo_x_coords, (x, y), method='linear', fill_value=np.nan)
                    if np.isnan(result):
                        result = griddata(camera_coords, servo_x_coords, (x, y), method='nearest')
                    return result
                else:
                    return griddata(camera_coords, servo_x_coords, (x, y), method='nearest')
            except Exception as e:
                print(f"⚠️ Interpolation error for X: {e}")
                return griddata(camera_coords, servo_x_coords, (x, y), method='nearest')

        def safe_interpolator_y(x, y):
            try:
                if len(points) >= 3:
                    result = griddata(camera_coords, servo_y_coords, (x, y), method='linear', fill_value=np.nan)
                    if np.isnan(result):
                        result = griddata(camera_coords, servo_y_coords, (x, y), method='nearest')
                    return result
                else:
                    return griddata(camera_coords, servo_y_coords, (x, y), method='nearest')
            except Exception as e:
                print(f"⚠️ Interpolation error for Y: {e}")
                return griddata(camera_coords, servo_y_coords, (x, y), method='nearest')

        interpolator_x = safe_interpolator_x
        interpolator_y = safe_interpolator_y

        print(f"✅ Calibration loaded successfully with {len(points)} points")
        return True

    except Exception as e:
        print(f"❌ Error loading calibration: {e}")
        return False

def camera_to_servo_angles(camera_x, camera_y):
    """Convert camera coordinates to servo angles using calibration data"""
    if interpolator_x is None or interpolator_y is None:
        print("❌ No calibration data loaded!")
        return 88, 85  # Return center position

    try:
        # Use interpolation to get servo angles
        servo_x = interpolator_x(camera_x, camera_y)
        servo_y = interpolator_y(camera_x, camera_y)

        # Handle None or NaN values
        if servo_x is None or servo_y is None or np.isnan(servo_x) or np.isnan(servo_y):
            print(f"⚠️ Interpolation failed for ({camera_x}, {camera_y}), using center position")
            return 88, 85

        # Constrain to servo ranges
        servo_x = max(SERVO_X_MIN, min(SERVO_X_MAX, float(servo_x)))
        servo_y = max(SERVO_Y_MIN, min(SERVO_Y_MAX, float(servo_y)))

        return int(servo_x), int(servo_y)

    except Exception as e:
        print(f"❌ Error in camera_to_servo_angles: {e}")
        return 88, 85  # Return center position as fallback

def test_calibration():
    """Test calibration accuracy using calibration points"""
    if not calibration_data:
        print("❌ No calibration data to test!")
        return

    print("\n=== CALIBRATION ACCURACY TEST ===")
    points = calibration_data['calibration_points']

    total_error_x = 0
    total_error_y = 0

    for i, point in enumerate(points):
        cam_x, cam_y = point['camera']
        expected_x, expected_y = point['servo']

        calculated_x, calculated_y = camera_to_servo_angles(cam_x, cam_y)

        error_x = abs(calculated_x - expected_x)
        error_y = abs(calculated_y - expected_y)

        total_error_x += error_x
        total_error_y += error_y

        print(f"Point {i+1}: Camera({cam_x},{cam_y}) -> Expected({expected_x}°,{expected_y}°) "
              f"Got({calculated_x}°,{calculated_y}°) Error({error_x}°,{error_y}°)")

    avg_error_x = total_error_x / len(points)
    avg_error_y = total_error_y / len(points)

    print(f"\n📊 Average Error: X={avg_error_x:.1f}°, Y={avg_error_y:.1f}°")
    print(f"📈 Total Calibration Points: {len(points)}")

def compute_center(box):
    """Compute center of bounding box"""
    x1, y1, x2, y2 = box
    return np.array([(x1 + x2) / 2, (y1 + y2) / 2])

# Removed old assign_id function - now using WeedTracker class

def draw_calibration_overlay(frame):
    """Draw calibration points and info on frame"""
    if not calibration_data:
        return

    # Draw calibration points
    points = calibration_data['calibration_points']
    for i, point in enumerate(points):
        cam_x, cam_y = point['camera']

        # Draw calibration point
        cv2.circle(frame, (int(cam_x), int(cam_y)), 6, (255, 255, 0), 2)
        cv2.putText(frame, f"C{i+1}", (int(cam_x) + 8, int(cam_y) - 8),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)

    # Draw calibration info
    cv2.putText(frame, f"Calibration Points: {len(points)}",
               (10, CAMERA_HEIGHT - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

def main():
    """Main weed burning loop"""
    print("🔥 WEED BURNING SYSTEM")
    print("=" * 50)

    # Load calibration data
    if not load_calibration():
        print("❌ Cannot start without calibration data!")
        return

    # Test calibration accuracy
    test_calibration()

    # Initialize servo to center
    servo_x.write(88)
    servo_y.write(85)
    time.sleep(1)

    print("\n🎯 Starting weed detection and burning...")
    print("Press 'q' to quit")

    frame_count = 0

    try:
        while True:
            frame = get_kinect_frame()
            frame = cv2.resize(frame, (CAMERA_WIDTH, CAMERA_HEIGHT))
            frame_height, frame_width = frame.shape[:2]
            center_x = frame_width // 2
            center_y = frame_height // 2
            frame_count += 1

            # Run YOLO detection (without built-in tracking to avoid conflicts)
            results = model(frame, conf=0.6, stream=True)

            # Collect all detections for this frame
            detections = []
            detection_boxes = []

            for result in results:
                boxes = result.boxes
                if boxes is None:
                    continue

                for box in boxes:
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    center = compute_center((x1, y1, x2, y2))
                    detections.append(center)
                    detection_boxes.append((x1, y1, x2, y2))

            # Update tracker with all detections
            tracked_objects = weed_tracker.update(detections)

            # Process each tracked object
            for i, (obj_id, center) in enumerate(tracked_objects):
                # Get corresponding bounding box
                if i < len(detection_boxes):
                    x1, y1, x2, y2 = detection_boxes[i]
                else:
                    # Use center to create approximate box for lost objects
                    box_size = 30
                    x1 = int(center[0] - box_size)
                    y1 = int(center[1] - box_size)
                    x2 = int(center[0] + box_size)
                    y2 = int(center[1] + box_size)

                # Convert center to servo angles using calibration
                box_center_x, box_center_y = int(center[0]), int(center[1])
                servo_angle_x, servo_angle_y = camera_to_servo_angles(box_center_x, box_center_y)

                # Get object state for better visualization
                obj_state = weed_tracker.objects[obj_id]['state']
                obj_confidence = weed_tracker.objects[obj_id]['confidence']

                if weed_tracker.is_burned(obj_id):
                    color = (0, 0, 255)  # Red for burned
                    label = f"ID:{obj_id} (BURNED)"
                elif obj_state == 'lost':
                    color = (0, 165, 255)  # Orange for lost objects
                    label = f"ID:{obj_id} (LOST)"
                else:
                    # Only burn active objects that are clearly detected
                    if obj_confidence > 0.7:  # Only burn high-confidence detections
                        # Target and burn the weed
                        servo_x.write(servo_angle_x)
                        servo_y.write(servo_angle_y)
                        laser.write(1)

                        print(f"🎯 Targeting ID:{obj_id} at angles X:{servo_angle_x}°, Y:{servo_angle_y}°")
                        print(f"   Target in camera: X:{box_center_x}, Y:{box_center_y}")
                        print(f"   Confidence: {obj_confidence:.2f}")

                        time.sleep(2)  # Burn time
                        laser.write(0)
                        weed_tracker.mark_burned(obj_id)

                        color = (0, 255, 0)  # Green for targeted
                        label = f"ID:{obj_id} (TARGETED)"
                    else:
                        color = (255, 255, 0)  # Yellow for low confidence
                        label = f"ID:{obj_id} (TRACKING)"

                # Draw detection with thickness based on confidence
                thickness = max(1, int(obj_confidence * 3))
                cv2.rectangle(frame, (x1, y1), (x2, y2), color, thickness)
                cv2.putText(frame, label, (x1, y1 - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

                # Draw center point and coordinates
                cv2.circle(frame, (box_center_x, box_center_y), 5, color, -1)
                cv2.putText(frame, f"({box_center_x},{box_center_y})",
                           (box_center_x + 10, box_center_y),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

                # Draw servo angles and confidence
                cv2.putText(frame, f"Servo:({servo_angle_x},{servo_angle_y})",
                           (box_center_x + 10, box_center_y + 15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
                cv2.putText(frame, f"Conf:{obj_confidence:.2f}",
                           (box_center_x + 10, box_center_y + 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

            # Draw interface elements
            cv2.line(frame, (center_x, 0), (center_x, frame_height), (255, 0, 0), 1)
            cv2.line(frame, (0, center_y), (frame_width, center_y), (255, 0, 0), 1)

            # Draw calibration overlay
            draw_calibration_overlay(frame)

            # Draw stats
            stats = weed_tracker.get_stats()
            cv2.putText(frame, f"Frame: {frame_count} | Active: {stats['active']} | Burned: {stats['burned']}",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"Lost: {stats['lost']} | Total: {stats['total']} | Motion: {stats['motion']:.1f}px",
                       (10, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # Display frame
            cv2.imshow("Weed Burning System", frame)

            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

    except KeyboardInterrupt:
        print("\n🛑 Stopping weed burning system...")

    finally:
        # Cleanup
        laser.write(0)
        servo_x.write(88)  # Return to center
        servo_y.write(85)
        board.exit()
        cv2.destroyAllWindows()
        print("✅ System shutdown complete")

if __name__ == "__main__":
    main()
