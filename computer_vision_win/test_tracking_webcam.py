#!/usr/bin/env python3
"""
Test the tracking system with webcam (without hardware dependencies)
This helps debug the tracking without needing Ki<PERSON> or Arduino
"""

from ultralytics import YOLO
import cv2
import numpy as np
import time

# Copy the WeedTracker class for testing
class WeedTracker:
    def __init__(self, max_disappeared=10, max_distance=40):
        self.next_id = 0
        self.objects = {}
        self.burned_ids = set()
        self.max_disappeared = max_disappeared
        self.max_distance = max_distance
        self.frame_count = 0
        
    def update(self, detections):
        self.frame_count += 1
        print(f"Tracker update: Frame {self.frame_count}, {len(detections)} detections, {len(self.objects)} existing objects")
        
        if len(detections) == 0:
            self._age_objects()
            return []
        
        if len(self.objects) == 0:
            print("  No existing objects, creating new ones")
            for detection in detections:
                new_id = self._create_new_object(detection)
                print(f"  Created object {new_id} at {detection}")
        else:
            print("  Matching detections to existing objects")
            self._match_detections(detections)
        
        self._cleanup_objects()
        
        active_objects = [(obj_id, obj_data['center']) for obj_id, obj_data in self.objects.items() 
                         if obj_data['state'] == 'active']
        print(f"  Returning {len(active_objects)} active objects")
        return active_objects
    
    def _create_new_object(self, center):
        self.objects[self.next_id] = {
            'center': center,
            'last_seen': self.frame_count,
            'state': 'active',
            'confidence': 1.0,
            'birth_frame': self.frame_count
        }
        self.next_id += 1
        return self.next_id - 1
    
    def _match_detections(self, detections):
        active_objects = {obj_id: obj_data for obj_id, obj_data in self.objects.items() 
                         if obj_data['state'] in ['active', 'lost']}
        
        if len(active_objects) == 0:
            for detection in detections:
                self._create_new_object(detection)
            return
        
        used_detections = set()
        used_objects = set()
        
        for i, detection in enumerate(detections):
            best_obj_id = None
            min_distance = self.max_distance
            
            for obj_id, obj_data in active_objects.items():
                if obj_id in used_objects:
                    continue
                    
                distance = np.linalg.norm(np.array(detection) - np.array(obj_data['center']))
                if distance < min_distance:
                    min_distance = distance
                    best_obj_id = obj_id
            
            if best_obj_id is not None:
                self.objects[best_obj_id]['center'] = detection
                self.objects[best_obj_id]['last_seen'] = self.frame_count
                self.objects[best_obj_id]['state'] = 'active'
                self.objects[best_obj_id]['confidence'] = min(1.0, self.objects[best_obj_id]['confidence'] + 0.2)
                used_objects.add(best_obj_id)
                used_detections.add(i)
                print(f"  Matched detection {i} to object {best_obj_id} (dist: {min_distance:.1f})")
            else:
                new_id = self._create_new_object(detection)
                print(f"  Created new object {new_id} for detection {i}")
        
        for obj_id in active_objects:
            if obj_id not in used_objects:
                if self.objects[obj_id]['state'] == 'active':
                    self.objects[obj_id]['state'] = 'lost'
                    self.objects[obj_id]['confidence'] = max(0.0, self.objects[obj_id]['confidence'] - 0.3)
                    print(f"  Object {obj_id} marked as lost")
    
    def _age_objects(self):
        for obj_id in self.objects:
            if self.objects[obj_id]['state'] == 'active':
                self.objects[obj_id]['state'] = 'lost'
            self.objects[obj_id]['confidence'] = max(0.0, self.objects[obj_id]['confidence'] - 0.1)
    
    def _cleanup_objects(self):
        to_remove = []
        for obj_id, obj_data in self.objects.items():
            frames_since_seen = self.frame_count - obj_data['last_seen']
            if (frames_since_seen > self.max_disappeared and 
                obj_data['state'] == 'lost' and 
                obj_id not in self.burned_ids):
                to_remove.append(obj_id)
        
        for obj_id in to_remove:
            print(f"  Removing old object {obj_id}")
            del self.objects[obj_id]
    
    def mark_burned(self, obj_id):
        if obj_id in self.objects:
            self.burned_ids.add(obj_id)
            self.objects[obj_id]['state'] = 'burned'
    
    def is_burned(self, obj_id):
        return obj_id in self.burned_ids
    
    def get_stats(self):
        active_count = sum(1 for obj in self.objects.values() if obj['state'] == 'active')
        lost_count = sum(1 for obj in self.objects.values() if obj['state'] == 'lost')
        burned_count = len(self.burned_ids)
        return {
            'active': active_count,
            'lost': lost_count,
            'burned': burned_count,
            'total': len(self.objects)
        }

def compute_center(box):
    x1, y1, x2, y2 = box
    return [(x1 + x2) / 2, (y1 + y2) / 2]

def main():
    # Load YOLO model
    try:
        model = YOLO("best.pt")
        print("✅ Loaded custom model: best.pt")
    except:
        try:
            model = YOLO("yolov8n.pt")
            print("✅ Loaded default model: yolov8n.pt")
        except:
            print("❌ Could not load any YOLO model")
            return
    
    # Initialize tracker
    tracker = WeedTracker(max_disappeared=10, max_distance=50)
    
    # Open webcam
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ Could not open webcam")
        return
    
    print("🎯 Starting tracking test with webcam...")
    print("Press 'q' to quit, 'b' to burn current objects")
    
    frame_count = 0
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            frame = cv2.resize(frame, (640, 480))
            
            # Run YOLO detection
            results = model(frame, conf=0.6, stream=True)
            
            # Collect detections
            current_detections = []
            for result in results:
                boxes = result.boxes
                if boxes is None:
                    continue
                
                for box in boxes:
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    center = compute_center((x1, y1, x2, y2))
                    current_detections.append({
                        'center': center,
                        'bbox': (x1, y1, x2, y2)
                    })
            
            # Extract centers for tracker
            detection_centers = [det['center'] for det in current_detections]
            
            # Update tracker
            tracked_objects = tracker.update(detection_centers)
            
            # Draw results
            for obj_id, center in tracked_objects:
                # Find matching detection for bbox
                best_detection = None
                min_dist = float('inf')
                for detection in current_detections:
                    dist = np.linalg.norm(np.array(center) - np.array(detection['center']))
                    if dist < min_dist:
                        min_dist = dist
                        best_detection = detection
                
                if best_detection and min_dist < 50:
                    x1, y1, x2, y2 = best_detection['bbox']
                else:
                    # Approximate box
                    x1, y1 = int(center[0] - 30), int(center[1] - 30)
                    x2, y2 = int(center[0] + 30), int(center[1] + 30)
                
                # Choose color based on state
                if tracker.is_burned(obj_id):
                    color = (0, 0, 255)  # Red
                    label = f"ID:{obj_id} (BURNED)"
                else:
                    color = (0, 255, 0)  # Green
                    label = f"ID:{obj_id} (ACTIVE)"
                
                # Draw
                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                cv2.putText(frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                cv2.circle(frame, (int(center[0]), int(center[1])), 5, color, -1)
            
            # Draw stats
            stats = tracker.get_stats()
            cv2.putText(frame, f"Frame: {frame_count} | Active: {stats['active']} | Burned: {stats['burned']}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"Lost: {stats['lost']} | Total: {stats['total']}", 
                       (10, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            cv2.imshow("Tracking Test", frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('b'):
                # Burn all active objects
                for obj_id, _ in tracked_objects:
                    if not tracker.is_burned(obj_id):
                        tracker.mark_burned(obj_id)
                        print(f"Burned object {obj_id}")
    
    except KeyboardInterrupt:
        print("\n🛑 Stopping...")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
        print("✅ Test complete")

if __name__ == "__main__":
    main()
