#!/usr/bin/env python3
"""
Test different YOLO tracking methods to see which ones work
"""

from ultralytics import YOLO
import cv2
import numpy as np

def test_trackers():
    """Test different tracking methods"""
    
    # Load model
    try:
        model = YOLO("best.pt")
        print("✅ Loaded custom model: best.pt")
    except:
        try:
            model = YOLO("yolov8n.pt")
            print("✅ Loaded default model: yolov8n.pt")
        except:
            print("❌ Could not load any model")
            return

    # Create a test frame
    test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    cv2.rectangle(test_frame, (100, 100), (200, 200), (255, 255, 255), -1)
    cv2.rectangle(test_frame, (300, 200), (400, 300), (255, 255, 255), -1)

    trackers_to_test = [
        "botsort.yaml",      # BoTSORT (SORT-based)
        "bytetrack.yaml",    # ByteTrack
        "sort.yaml",         # Classic SORT (if available)
    ]

    print("\n🧪 Testing available trackers...")
    print("=" * 50)

    working_trackers = []

    for tracker in trackers_to_test:
        print(f"\n🔍 Testing {tracker}...")
        try:
            results = model.track(test_frame, conf=0.1, persist=True, tracker=tracker)
            print(f"✅ {tracker} - SUCCESS")
            working_trackers.append(tracker)
            
            # Check if results have tracking IDs
            for result in results:
                if result.boxes is not None and len(result.boxes) > 0:
                    for box in result.boxes:
                        if hasattr(box, 'id') and box.id is not None:
                            print(f"   📍 Found tracking ID: {int(box.id[0])}")
                        else:
                            print(f"   ⚠️ No tracking ID found")
                        break
                    break
                    
        except Exception as e:
            print(f"❌ {tracker} - FAILED: {e}")

    print(f"\n📊 Summary:")
    print(f"Working trackers: {working_trackers}")
    
    if working_trackers:
        print(f"✅ Recommended tracker: {working_trackers[0]}")
    else:
        print("❌ No trackers working - will use detection only")

    # Test detection without tracking
    print(f"\n🔍 Testing detection without tracking...")
    try:
        results = model(test_frame, conf=0.1, stream=True)
        print(f"✅ Detection without tracking - SUCCESS")
    except Exception as e:
        print(f"❌ Detection without tracking - FAILED: {e}")

if __name__ == "__main__":
    test_trackers()
