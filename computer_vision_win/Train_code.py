from ultralytics import YOLO

# تحميل موديل YOLOv8s (أدق من nano)
model = YOLO("yolov8n.pt")

# تدريب الموديل
model.train(
    data=r"D:/computer_vision_UNI/logo/Mini_logo.v2i.yolov8/data.yaml",  # مسار ملف الداتا
    epochs=50,            # عدد الإيبوكس
    imgsz=640,            # حجم الصور
    batch=8,              # حجم الباتش
    patience=10,          # عدد الإيبوكس اللي ينتظرها لو مفيش تحسن
    lr0=0.005,            # معدل التعلم الابتدائي
    weight_decay=0.001,   # تقليل الأوفر فيتنج
    optimizer="AdamW"     # استخدام Optimizer أدق
)
