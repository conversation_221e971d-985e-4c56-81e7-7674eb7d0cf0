from ultralytics import YOLO
import cv2
import freenect
import numpy as np
import time
import pyfirmata
import json
import glob
import os
from scipy.interpolate import griddata

# Load YOLO model
model = YOLO("best.pt")

# Arduino setup
board = pyfirmata.Arduino('/dev/ttyACM0')  # Change path as needed
it = pyfirmata.util.Iterator(board)
it.start()

# Define servo and laser pins
servo_x = board.get_pin('d:10:s')     # D10: Servo X
servo_y = board.get_pin('d:9:s')      # D9: Servo Y
laser = board.get_pin('d:8:o')        # D8: Laser ON/OFF

# Camera dimensions
CAMERA_WIDTH = 640
CAMERA_HEIGHT = 480

# Servo ranges (will be loaded from calibration)
SERVO_X_MIN = 70
SERVO_X_MAX = 120
SERVO_Y_MIN = 70
SERVO_Y_MAX = 122

# Simple tracking using YOLO's built-in tracker
burned_ids = set()  # Set of burned object IDs

# Calibration data
calibration_data = None
interpolator_x = None
interpolator_y = None

def get_kinect_frame():
    """Capture frame from Kinect v1"""
    _, _ = freenect.sync_get_depth()
    video, _ = freenect.sync_get_video()
    return cv2.cvtColor(video, cv2.COLOR_RGB2BGR)

def load_calibration():
    """Load the most recent calibration file"""
    global calibration_data, interpolator_x, interpolator_y
    global SERVO_X_MIN, SERVO_X_MAX, SERVO_Y_MIN, SERVO_Y_MAX

    files = glob.glob("servo_calibration_*.json")
    if not files:
        print("❌ No calibration files found! Please run calibrate_servo.py first.")
        print("💡 Creating a basic single-point calibration as fallback...")

        # Create a basic calibration with your original point
        basic_calibration = {
            "timestamp": "fallback",
            "servo_ranges": {
                "x_min": SERVO_X_MIN,
                "x_max": SERVO_X_MAX,
                "y_min": SERVO_Y_MIN,
                "y_max": SERVO_Y_MAX
            },
            "camera_dimensions": {
                "width": CAMERA_WIDTH,
                "height": CAMERA_HEIGHT
            },
            "calibration_points": [
                {"camera": [136, 234], "servo": [76, 92]},
                {"camera": [320, 240], "servo": [88, 85]},  # Center point
                {"camera": [500, 150], "servo": [100, 78]}  # Another point for interpolation
            ]
        }

        # Save basic calibration
        with open("servo_calibration_fallback.json", 'w') as f:
            json.dump(basic_calibration, f, indent=2)

        print("✅ Created fallback calibration file")
        files = ["servo_calibration_fallback.json"]

    latest_file = max(files, key=os.path.getctime)
    try:
        with open(latest_file, 'r') as f:
            calibration_data = json.load(f)

        print(f"📂 Loaded calibration from: {latest_file}")

        # Update servo ranges from calibration
        ranges = calibration_data['servo_ranges']
        SERVO_X_MIN = ranges['x_min']
        SERVO_X_MAX = ranges['x_max']
        SERVO_Y_MIN = ranges['y_min']
        SERVO_Y_MAX = ranges['y_max']

        # Create interpolators for smooth mapping
        points = calibration_data['calibration_points']
        if len(points) < 1:
            print("❌ Error: No calibration points found!")
            return False
        elif len(points) < 3:
            print("⚠️ Warning: Less than 3 calibration points. Using nearest neighbor interpolation.")

        # Extract camera and servo coordinates
        camera_coords = np.array([point['camera'] for point in points])
        servo_x_coords = np.array([point['servo'][0] for point in points])
        servo_y_coords = np.array([point['servo'][1] for point in points])

        # Create interpolators with error handling
        def safe_interpolator_x(x, y):
            try:
                if len(points) >= 3:
                    result = griddata(camera_coords, servo_x_coords, (x, y), method='linear', fill_value=np.nan)
                    if np.isnan(result):
                        result = griddata(camera_coords, servo_x_coords, (x, y), method='nearest')
                    return result
                else:
                    return griddata(camera_coords, servo_x_coords, (x, y), method='nearest')
            except Exception as e:
                print(f"⚠️ Interpolation error for X: {e}")
                return griddata(camera_coords, servo_x_coords, (x, y), method='nearest')

        def safe_interpolator_y(x, y):
            try:
                if len(points) >= 3:
                    result = griddata(camera_coords, servo_y_coords, (x, y), method='linear', fill_value=np.nan)
                    if np.isnan(result):
                        result = griddata(camera_coords, servo_y_coords, (x, y), method='nearest')
                    return result
                else:
                    return griddata(camera_coords, servo_y_coords, (x, y), method='nearest')
            except Exception as e:
                print(f"⚠️ Interpolation error for Y: {e}")
                return griddata(camera_coords, servo_y_coords, (x, y), method='nearest')

        interpolator_x = safe_interpolator_x
        interpolator_y = safe_interpolator_y

        print(f"✅ Calibration loaded successfully with {len(points)} points")
        return True

    except Exception as e:
        print(f"❌ Error loading calibration: {e}")
        return False

def camera_to_servo_angles(camera_x, camera_y):
    """Convert camera coordinates to servo angles using calibration data"""
    if interpolator_x is None or interpolator_y is None:
        print("❌ No calibration data loaded!")
        return 88, 85  # Return center position

    try:
        # Use interpolation to get servo angles
        servo_x = interpolator_x(camera_x, camera_y)
        servo_y = interpolator_y(camera_x, camera_y)

        # Handle None or NaN values
        if servo_x is None or servo_y is None or np.isnan(servo_x) or np.isnan(servo_y):
            print(f"⚠️ Interpolation failed for ({camera_x}, {camera_y}), using center position")
            return 88, 85

        # Constrain to servo ranges
        servo_x = max(SERVO_X_MIN, min(SERVO_X_MAX, float(servo_x)))
        servo_y = max(SERVO_Y_MIN, min(SERVO_Y_MAX, float(servo_y)))

        return int(servo_x), int(servo_y)

    except Exception as e:
        print(f"❌ Error in camera_to_servo_angles: {e}")
        return 88, 85  # Return center position as fallback

def compute_center(box):
    """Compute center of bounding box"""
    x1, y1, x2, y2 = box
    return np.array([(x1 + x2) / 2, (y1 + y2) / 2])

def draw_calibration_overlay(frame):
    """Draw calibration points and info on frame"""
    if not calibration_data:
        return

    # Draw calibration points
    points = calibration_data['calibration_points']
    for i, point in enumerate(points):
        cam_x, cam_y = point['camera']

        # Draw calibration point
        cv2.circle(frame, (int(cam_x), int(cam_y)), 6, (255, 255, 0), 2)
        cv2.putText(frame, f"C{i+1}", (int(cam_x) + 8, int(cam_y) - 8),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)

    # Draw calibration info
    cv2.putText(frame, f"Calibration Points: {len(points)}",
               (10, CAMERA_HEIGHT - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

def main():
    """Main weed burning loop"""
    print("🔥 WEED BURNING SYSTEM - SIMPLE TRACKING")
    print("=" * 50)

    # Load calibration data
    if not load_calibration():
        print("❌ Cannot start without calibration data!")
        return

    # Initialize servo to center
    servo_x.write(88)
    servo_y.write(85)
    time.sleep(1)

    print("\n🎯 Starting weed detection and burning...")
    print("Press 'q' to quit")

    frame_count = 0

    try:
        while True:
            frame = get_kinect_frame()
            frame = cv2.resize(frame, (CAMERA_WIDTH, CAMERA_HEIGHT))
            frame_height, frame_width = frame.shape[:2]
            center_x = frame_width // 2
            center_y = frame_height // 2
            frame_count += 1

            # Run YOLO detection with built-in tracking
            results = model.track(frame, conf=0.5, persist=True, tracker="bytetrack.yaml")

            for result in results:
                boxes = result.boxes
                if boxes is None:
                    continue

                for box in boxes:
                    # Get bounding box coordinates
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    
                    # Get track ID from YOLO tracker
                    if box.id is not None:
                        track_id = int(box.id[0])
                    else:
                        continue  # Skip if no track ID
                    
                    # Get confidence
                    confidence = float(box.conf[0])
                    
                    # Compute center
                    center = compute_center((x1, y1, x2, y2))
                    box_center_x, box_center_y = int(center[0]), int(center[1])
                    
                    # Convert center to servo angles using calibration
                    servo_angle_x, servo_angle_y = camera_to_servo_angles(box_center_x, box_center_y)

                    print(f"Track ID: {track_id}, Center: ({box_center_x}, {box_center_y}), Conf: {confidence:.2f}")

                    # Check if this weed has already been burned
                    if track_id in burned_ids:
                        color = (0, 0, 255)  # Red for burned
                        label = f"ID:{track_id} (BURNED)"
                    else:
                        # Only burn if confidence is high enough
                        if confidence > 0.7:
                            # Target and burn the weed
                            servo_x.write(servo_angle_x)
                            servo_y.write(servo_angle_y)
                            laser.write(1)

                            print(f"🎯 Targeting ID:{track_id} at angles X:{servo_angle_x}°, Y:{servo_angle_y}°")
                            print(f"   Target in camera: X:{box_center_x}, Y:{box_center_y}")

                            time.sleep(2)  # Burn time
                            laser.write(0)
                            burned_ids.add(track_id)

                            color = (0, 255, 0)  # Green for targeted
                            label = f"ID:{track_id} (TARGETED)"
                        else:
                            color = (255, 255, 0)  # Yellow for low confidence
                            label = f"ID:{track_id} (TRACKING)"

                    # Draw detection
                    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                    cv2.putText(frame, label, (x1, y1 - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

                    # Draw center point and coordinates
                    cv2.circle(frame, (box_center_x, box_center_y), 5, color, -1)
                    cv2.putText(frame, f"({box_center_x},{box_center_y})",
                               (box_center_x + 10, box_center_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

                    # Draw servo angles and confidence
                    cv2.putText(frame, f"Servo:({servo_angle_x},{servo_angle_y})",
                               (box_center_x + 10, box_center_y + 15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
                    cv2.putText(frame, f"Conf:{confidence:.2f}",
                               (box_center_x + 10, box_center_y + 30),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

            # Draw interface elements
            cv2.line(frame, (center_x, 0), (center_x, frame_height), (255, 0, 0), 1)
            cv2.line(frame, (0, center_y), (frame_width, center_y), (255, 0, 0), 1)

            # Draw calibration overlay
            draw_calibration_overlay(frame)

            # Draw stats
            cv2.putText(frame, f"Frame: {frame_count} | Burned: {len(burned_ids)}",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Display frame
            cv2.imshow("Weed Burning System", frame)

            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

    except KeyboardInterrupt:
        print("\n🛑 Stopping weed burning system...")

    finally:
        # Cleanup
        laser.write(0)
        servo_x.write(88)  # Return to center
        servo_y.write(85)
        board.exit()
        cv2.destroyAllWindows()
        print("✅ System shutdown complete")

if __name__ == "__main__":
    main()
